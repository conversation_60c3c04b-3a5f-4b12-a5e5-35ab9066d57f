import { envData } from '../fixtures/envData';

export const addEnvToConfig = (
  _on: Cypress.PluginEvents,
  config: Cypress.PluginConfigOptions
): Cypress.PluginConfigOptions => {
  const env = config.env.ENVIRONMENT;
  if (env && env in envData) {
    const envConfig = envData[env as keyof typeof envData];
    config.baseUrl = envConfig.baseUrl;
    config.env.apiRoot = envConfig.apiRoot;
    config.env.glcApiRoot = envConfig.glcApiRoot;
  } else {
    if (!config.env.apiRoot) {
      // use config.json
      config.env.apiRoot = 'https://api.stage.us-gov-2.veritone.com';
      config.env.glcApiRoot = 'https://glc-backend.stage.us-gov-2.veritone.com';
    }
  }
  return config;
};
