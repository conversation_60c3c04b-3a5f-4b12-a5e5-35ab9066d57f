Feature: Results tab

  # Scenario: Prepare test data
  #   Given The user uploads file "upload_test.mp4" with transcription "head, person, transcription"

  Scenario Outline: Video tab, Filter by <type>
    Given The user goes to test file "upload_test.mp4"
    When The user navigates to Editor tab
    Given each detection name should not be empty
    And I have unselected all filters
    Then no detection should be visible
    When I filter the video tab by "<type>"
    Then every result detection should contain "<type>"

    Examples:
      | type   |
      | Head   |
      | Person |

  Scenario: Audio tab
    And The user goes to test file "upload_test.mp4"
    When I click the audio tab button
    Then I should see "online" in file details transcript

  # @e2e
  # Scenario: Delete test files
  #   Given The user deletes files
  #     | upload_test.mp4 |
