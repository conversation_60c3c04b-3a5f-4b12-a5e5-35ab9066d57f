{"extends": "../tsconfig.json", "compilerOptions": {"baseUrl": "./", "typeRoots": ["../node_modules/@types", "../node_modules/veritone-types/@types", "../types/"], "paths": {"@cbsa/*": ["../src/cbsa-app/*"], "@common/*": ["../src/common-app/*"], "@redact/*": ["../src/redact-app/*"], "@resources/*": ["../resources/*"], "@cbsa-reduxElements": ["../src/cbsa-app/reduxElements/index"], "@cbsa-reduxElements/*": ["../src/cbsa-app/reduxElements/*"], "@cbsa-components/*": ["../src/cbsa-app/components/*"], "@common-components/*": ["../src/common-app/components/*"], "@redact-components/*": ["../src/redact-app/components/*"], "@helpers/*": ["../src/helpers/*"], "@cbsa-modules/*": ["../src/cbsa-app/state/modules/*"], "@common-modules/*": ["../src/common-app/state/modules/*"], "@redact-modules/*": ["../src/redact-app/state/modules/*"], "@cbsa-pages/*": ["../src/cbsa-app/pages/*"], "@common-pages/*": ["../src/common-app/pages/*"], "@redact-pages/*": ["../src/redact-app/pages/*"], "@redact-routing": ["../src/redact-app/state/modules/routing/index"], "@redact-routing/*": ["../src/redact-app/state/modules/routing/*"], "@cbsa-state/*": ["../src/cbsa-app/state/*"], "@cbsa-state": ["../src/cbsa-app/state/index"], "@common-state/*": ["../src/common-app/state/*"], "@common-state": ["../src/common-app/state/index"], "@redact-state/*": ["../src/redact-app/state/*"], "@redact-state": ["../src/redact-app/state/index"], "@utils": ["../src/utils/index"], "@redact-shared/*": ["../src/redact-app/shared/*"], "@user-onboarding/components/*": ["../src/common-app/user-onboarding/components/*"], "@user-onboarding": ["../src/common-app/user-onboarding/index"], "@user-permissions": ["../src/common-app/user-permissions/index"], "@worker/*": ["../src/common-app/web-worker/*"], "@worker": ["../src/common-app/web-worker/index"], "@redact-test/*": ["../test/redact-app/*"], "@test/*": ["../test/*"], "@i18n": ["../src/common-app/i18n/index"], "src/*": ["../src/*"]}}, "files": ["../node_modules/shaka-player/dist/shaka-player.compiled.d.ts", "./index.d.ts", "../src/redact-app/types.d.ts", "../src/common-app/web-worker/worker.ts", "../src/redact-app/index.tsx", "../src/cbsa-app/index.tsx", "../src/cbsa-app/types.d.ts"], "include": ["**/*.ts", "**/*.tsx", "../node_modules/cypress", "./cypress.config.ts"], "exclude": ["node_modules"]}