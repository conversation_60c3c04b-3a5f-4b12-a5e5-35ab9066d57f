import {
  DataTestSelectorRightPanelVideo,
  DataTestSelector,
} from '../../../support/helperFunction/mediaDetailHelper';
import { mediaDetailPage } from '../mediaDetail.po';

export const createMergedGroup = () => {
  cy.get(
    `[data-testid=${DataTestSelectorRightPanelVideo.ListSortContainer}] [data-testid=${DataTestSelectorRightPanelVideo.CheckBoxContainer}]`
  ).each(($container) => {
    if ($container.find('svg').length > 0) {
      cy.wrap($container).click();
    }
  });

  cy.getDataIdCy({ idAlias: DataTestSelector.ClusterRow })
    .filter(':contains("PERSON")')
    .eq(0)
    .within(() => {
      mediaDetailPage.nameGroup().click();
      mediaDetailPage.nameGroupInput().find('input').clear();
      mediaDetailPage.nameGroupInput().find('input').type('Group 1');
      mediaDetailPage.acceptIcon().click();
      mediaDetailPage.nameGroup().find('p').contains('Group 1');
    });

  cy.getDataIdCy({ idAlias: DataTestSelector.ClusterRow })
    .filter(':contains("HEAD")')
    .eq(0)
    .within(() => {
      mediaDetailPage.nameGroup().click();
      mediaDetailPage.nameGroupInput().find('input').clear();
      mediaDetailPage.nameGroupInput().find('input').type('Group 2');
      mediaDetailPage.acceptIcon().click();
      mediaDetailPage.nameGroup().find('p').contains('Group 2');
    });

  mediaDetailPage.groupItemMenu().eq(1).click({ force: true });
  mediaDetailPage.mergedWidthNamedGroup().click();
  cy.get('[role="button"]').contains('Group 1').click();
};

export const warningModalView = (
  header: string,
  body1: string,
  body2: string
): void => {
  mediaDetailPage.confirmDialogTitle().contains(header).should('be.visible');
  mediaDetailPage.confirmDialogContent().contains(body1).should('be.visible');
  mediaDetailPage.confirmDialogContent().contains(body2).should('be.visible');
};

export const verifyButtonInMergeGroup = (color: string): void => {
  mediaDetailPage
    .confirmDialogButton()
    .contains('Cancel')
    .toHaveCssProperty(
      'background',
      'rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box'
    );
  mediaDetailPage
    .confirmDialogButton()
    .contains('Group')
    .toHaveCssProperty('background-color', color);
  mediaDetailPage
    .confirmDialogButton()
    .contains('Merged Group')
    .toHaveCssProperty('background-color', color);
};

export const warningModalRadioButtonView = (
  header: string,
  body1: string,
  body2: string,
  body3: string
): void => {
  mediaDetailPage.confirmDialogTitle().contains(header).should('be.visible');
  mediaDetailPage.confirmDialogContent().find('p').should('contain', body1);
  mediaDetailPage.confirmDialogContent().contains(body3);

  cy.get('[id="demo-row-radio-buttons-group-label"]')
    .contains(body2)
    .should('be.visible');
  cy.get('[name="row-radio-buttons-group"]')
    .eq(0)
    .should('have.value', 'ellipse');
  cy.get('[name="row-radio-buttons-group"]')
    .eq(1)
    .should('have.value', 'rectangle');
  cy.get('[name="row-radio-buttons-group"]')
    .eq(2)
    .should('have.value', 'default');

  mediaDetailPage.confirmDialogButton().contains('Cancel').should('be.visible');
  mediaDetailPage.confirmDialogButton().contains('Group').should('be.visible');
  mediaDetailPage
    .confirmDialogButton()
    .contains('Merged Group')
    .should('be.visible');
};

export const verifyButtonInRedContainer = (): void => {
  mediaDetailPage.confirmDialogButton().contains('Cancel').should('be.visible');
  mediaDetailPage.confirmDialogButton().contains('Group').should('be.visible');
  mediaDetailPage
    .confirmDialogButton()
    .contains('Merged Group')
    .should('be.visible');
};

export const created2ndMergedGroup = () => {
  cy.get(
    `[data-testid=${DataTestSelectorRightPanelVideo.ListSortContainer}] [data-testid=${DataTestSelectorRightPanelVideo.CheckBoxContainer}]`
  ).each(($container) => {
    if ($container.find('svg').length > 0) {
      cy.wrap($container).click();
    }
  });

  // Handle first HEAD group
  cy.getDataIdCy({ idAlias: DataTestSelector.ClusterRow })
    .filter(':contains("HEAD")')
    .first()
    .within(() => {
      mediaDetailPage.nameGroup().click();
      mediaDetailPage.nameGroupInput().find('input').clear();
      mediaDetailPage.nameGroupInput().find('input').type('Group 1');
      mediaDetailPage.acceptIcon().click();
      mediaDetailPage.nameGroup().find('p').contains('Group 1');
    });

  // Handle second HEAD group if it exists
  cy.getDataIdCy({ idAlias: DataTestSelector.ClusterRow })
    .filter(':contains("HEAD")')
    .eq(1)
    .then(($secondHeadRow) => {
      if ($secondHeadRow.length > 0) {
        cy.wrap($secondHeadRow).within(() => {
          mediaDetailPage.nameGroup().click();
          mediaDetailPage.nameGroupInput().find('input').clear();
          mediaDetailPage.nameGroupInput().find('input').type('Group 2');
          mediaDetailPage.acceptIcon().click();
          mediaDetailPage.nameGroup().find('p').contains('Group 2');
        });
      }
      return undefined;
    });

  mediaDetailPage.groupItemMenu().eq(1).click({ force: true });
  mediaDetailPage.mergedWidthNamedGroup().click();
  cy.get('[role="button"]').contains('Group 1').click();
};
